#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
继续教育视频自动学习程序 - GUI版本
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import pyautogui
import win32gui
from datetime import datetime
import sys
import io

class VideoStudyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("继续教育视频自动学习程序")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        # 程序状态
        self.is_running = False
        self.chrome_window = None
        self.check_interval = 5
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # URL识别设置
        ttk.Label(main_frame, text="视频页面识别URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.url_pattern = tk.StringVar(value="https://gp.chinahrt.com/index.html#/v_video?")
        url_entry = ttk.Entry(main_frame, textvariable=self.url_pattern, width=60)
        url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 停止检测次数设置
        ttk.Label(main_frame, text="停止检测次数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.stop_count = tk.StringVar(value="40")
        stop_count_frame = ttk.Frame(main_frame)
        stop_count_frame.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        stop_count_entry = ttk.Entry(stop_count_frame, textvariable=self.stop_count, width=10)
        stop_count_entry.pack(side=tk.LEFT)
        ttk.Label(stop_count_frame, text="次 (连续检测到停止的次数，建议30-50次)").pack(side=tk.LEFT, padx=(5, 0))

        # 图像变化阈值设置
        ttk.Label(main_frame, text="图像变化阈值:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.change_threshold = tk.StringVar(value="0.03")
        threshold_frame = ttk.Frame(main_frame)
        threshold_frame.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        threshold_entry = ttk.Entry(threshold_frame, textvariable=self.change_threshold, width=10)
        threshold_entry.pack(side=tk.LEFT)
        ttk.Label(threshold_frame, text="(0.01-0.10，数值越小越敏感，建议0.02-0.05)").pack(side=tk.LEFT, padx=(5, 0))
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        # 开始/停止按钮
        self.start_button = ttk.Button(button_frame, text="开始学习", command=self.start_study)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止学习", command=self.stop_study, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 清空日志按钮
        clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建滚动文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.root.after(0, self._update_log, log_entry)
        
    def _update_log(self, message):
        """更新日志显示"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def update_status(self, status):
        """更新状态栏"""
        self.root.after(0, lambda: self.status_var.set(status))
        
    def start_study(self):
        """开始学习"""
        if self.is_running:
            return

        # 验证URL模式
        url_pattern = self.url_pattern.get().strip()
        if not url_pattern:
            messagebox.showerror("错误", "请输入视频页面识别URL")
            return

        # 验证停止检测次数
        try:
            stop_count = int(self.stop_count.get().strip())
            if stop_count <= 0:
                messagebox.showerror("错误", "停止检测次数必须大于0")
                return
        except ValueError:
            messagebox.showerror("错误", "停止检测次数必须是有效数字")
            return

        # 验证图像变化阈值
        try:
            change_threshold = float(self.change_threshold.get().strip())
            if change_threshold <= 0 or change_threshold > 1:
                messagebox.showerror("错误", "图像变化阈值必须在0-1之间")
                return
        except ValueError:
            messagebox.showerror("错误", "图像变化阈值必须是有效数字")
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 在新线程中运行学习程序
        study_thread = threading.Thread(target=self.run_study, args=(url_pattern, stop_count, change_threshold))
        study_thread.daemon = True
        study_thread.start()
        
    def stop_study(self):
        """停止学习"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.update_status("已停止")
        self.log_message("用户停止了学习程序")
        
    def run_study(self, url_pattern, stop_count, change_threshold):
        """运行学习程序"""
        try:
            self.update_status("正在运行...")
            self.log_message("🎓 继续教育视频自动学习程序启动")
            self.log_message(f"视频识别URL: {url_pattern}")
            self.log_message(f"停止检测次数: {stop_count} 次")
            self.log_message(f"图像变化阈值: {change_threshold} ({change_threshold*100:.1f}%)")
            
            # 查找Chrome窗口
            if not self.find_chrome_window():
                self.log_message("❌ 未找到Chrome浏览器窗口")
                self.stop_study()
                return
                
            # 检测标签页
            tab_count = self.get_tab_count(url_pattern)
            if tab_count == 0:
                self.log_message("❌ 未检测到任何标签页")
                self.stop_study()
                return
                
            # 开始学习循环
            self.study_loop(tab_count, url_pattern, stop_count, change_threshold)
            
        except Exception as e:
            self.log_message(f"❌ 程序异常: {e}")
        finally:
            if self.is_running:
                self.stop_study()
                
    def find_chrome_window(self):
        """查找Chrome窗口"""
        self.log_message("正在查找Chrome浏览器窗口...")
        
        chrome_windows = []
        
        def callback(hwnd, data):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                
                if (class_name == "Chrome_WidgetWin_1" and 
                    "Visual Studio Code" not in title and 
                    "Cursor" not in title and
                    "- Google Chrome" in title):
                    chrome_windows.append({'hwnd': hwnd, 'title': title})
            return True
        
        win32gui.EnumWindows(callback, None)
        
        if chrome_windows:
            self.chrome_window = chrome_windows[0]
            self.log_message(f"✓ 找到Chrome窗口: {self.chrome_window['title']}")
            return True
        else:
            return False
            
    def get_current_url(self):
        """获取当前标签页完整URL - 确保获取完整URL"""
        try:
            # 清空剪贴板
            import pyperclip
            pyperclip.copy("")

            # 多次尝试获取完整URL
            for attempt in range(3):  # 最多尝试3次
                # 选中地址栏
                pyautogui.hotkey('ctrl', 'l')
                time.sleep(1.5)  # 增加等待时间

                # 全选地址栏内容
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(1)  # 确保全选完成

                # 复制URL
                pyautogui.hotkey('ctrl', 'c')
                time.sleep(1.5)  # 增加等待时间确保复制完成

                # 获取剪贴板内容
                url = pyperclip.paste()

                # 按ESC取消选中
                pyautogui.press('escape')
                time.sleep(0.5)

                # 验证URL是否有效且完整
                if url and len(url) > 20 and ('http' in url or 'https' in url):
                    # 检查URL是否看起来完整（包含必要的参数）
                    if 'gp.chinahrt.com' in url and ('sectionId=' in url or 'courseId=' in url):
                        full_url = url.strip()
                        self.log_message(f"获取到完整URL (长度: {len(full_url)}): {full_url}")
                        return full_url
                    elif url.startswith('http'):
                        # 即使不是视频页面，也返回完整URL
                        full_url = url.strip()
                        self.log_message(f"获取到URL (长度: {len(full_url)}): {full_url[:100]}...")
                        return full_url

                # 如果获取失败，等待后重试
                self.log_message(f"第 {attempt + 1} 次获取URL失败，重试...")
                time.sleep(2)

            self.log_message("多次尝试后仍无法获取有效URL")
            return ""

        except Exception as e:
            self.log_message(f"获取URL异常: {e}")
            return ""
            
    def get_tab_count(self, url_pattern):
        """检测标签页数量 - 改进版本"""
        self.log_message("正在检测标签页数量（改进版URL检测）...")

        try:
            hwnd = self.chrome_window['hwnd']
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(2)  # 增加等待时间

            seen_urls = set()
            tab_count = 0
            consecutive_failures = 0
            max_failures = 3  # 允许连续3次失败

            for i in range(1, 51):  # 最多检测50个标签页
                if not self.is_running:
                    break

                try:
                    self.log_message(f"正在检测第 {i} 个标签页...")

                    # 统一使用Ctrl+Tab切换标签页
                    if i == 1:
                        # 第一个标签页，确保从第一个开始
                        pyautogui.hotkey('ctrl', '1')
                        self.log_message(f"    切换到第1个标签页")
                    else:
                        # 从第2个标签页开始，都使用Ctrl+Tab逐个切换
                        pyautogui.hotkey('ctrl', 'tab')
                        self.log_message(f"    使用 Ctrl+Tab 切换到第 {i} 个标签页")

                    time.sleep(2.5)  # 增加等待时间，确保页面完全加载

                    # 获取完整URL
                    current_url = self.get_current_url()

                    if current_url and len(current_url) > 20:
                        # 检查URL是否完全重复（完整字符串匹配）
                        if current_url in seen_urls:
                            self.log_message(f"检测到完全重复的URL:")
                            self.log_message(f"  重复URL: {current_url}")
                            self.log_message(f"✓ 检测完成，共找到 {tab_count} 个不重复标签页")
                            break

                        # 添加完整URL到已见URL集合
                        seen_urls.add(current_url)
                        tab_count = i
                        consecutive_failures = 0  # 重置失败计数

                        # 显示完整URL信息（不截断）
                        self.log_message(f"  第 {i} 个标签页完整URL:")
                        self.log_message(f"    {current_url}")

                        # 检查是否是视频页面
                        if current_url.startswith(url_pattern):
                            self.log_message(f"  ✓ 这是视频页面")
                            # 提取视频信息
                            if 'sectionName=' in current_url:
                                try:
                                    import urllib.parse
                                    section_name = current_url.split('sectionName=')[1].split('&')[0]
                                    decoded_name = urllib.parse.unquote(section_name, encoding='utf-8')
                                    self.log_message(f"    课程名: {decoded_name}")
                                except:
                                    pass
                        else:
                            self.log_message(f"  - 非视频页面")

                    else:
                        consecutive_failures += 1
                        self.log_message(f"  第 {i} 个标签页获取URL失败 ({consecutive_failures}/{max_failures})")

                        if consecutive_failures >= max_failures:
                            self.log_message(f"连续 {max_failures} 次获取URL失败，停止检测")
                            break

                except Exception as e:
                    consecutive_failures += 1
                    self.log_message(f"  第 {i} 个标签页检测出错: {e} ({consecutive_failures}/{max_failures})")

                    if consecutive_failures >= max_failures:
                        self.log_message(f"连续 {max_failures} 次检测出错，停止检测")
                        break

            self.log_message(f"✓ 标签页检测完成，总计: {tab_count} 个标签页，{len(seen_urls)} 个不同URL")
            return tab_count

        except Exception as e:
            self.log_message(f"检测标签页失败: {e}")
            return 0

    def switch_to_tab(self, tab_index):
        """切换到指定标签页"""
        try:
            hwnd = self.chrome_window['hwnd']
            if win32gui.IsWindow(hwnd):
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(1)

            # 统一使用Ctrl+Tab方式切换到指定标签页
            if tab_index <= 8:
                # 前8个标签页可以直接使用数字键
                pyautogui.hotkey('ctrl', str(tab_index))
            else:
                # 超过8个标签页时，先回到第1个，然后用Ctrl+Tab切换
                pyautogui.hotkey('ctrl', '1')
                time.sleep(0.5)
                for _ in range(tab_index - 1):
                    pyautogui.hotkey('ctrl', 'tab')
                    time.sleep(0.3)

            time.sleep(3)
            return True
        except:
            return False

    def is_video_page(self, url_pattern):
        """检测是否是视频页面"""
        try:
            current_url = self.get_current_url()
            return current_url.startswith(url_pattern)
        except:
            return False

    def start_video(self):
        """启动视频播放 - 简化版本"""
        try:
            time.sleep(1)
            pyautogui.press('space')
            self.log_message("✓ 已按空格键启动播放")
            time.sleep(2)  # 等待视频开始
        except Exception as e:
            self.log_message(f"播放控制出错: {e}")

    def is_video_playing(self, change_threshold):
        """检测视频是否在播放"""
        try:
            width, height = pyautogui.size()
            region = (width//2-150, height//2-100, 300, 200)

            img1 = pyautogui.screenshot(region=region)
            time.sleep(2)
            img2 = pyautogui.screenshot(region=region)

            pixels1 = list(img1.getdata())
            pixels2 = list(img2.getdata())

            diff_count = sum(1 for p1, p2 in zip(pixels1, pixels2) if p1 != p2)
            total_pixels = len(pixels1)
            change_ratio = diff_count / total_pixels

            return change_ratio > change_threshold
        except:
            return True

    def wait_for_completion(self, max_consecutive, change_threshold):
        """等待视频播放完成"""
        self.log_message(f"开始监控视频播放状态（每5秒检测一次，连续{max_consecutive}次停止后切换）...")

        consecutive_stopped = 0
        check_count = 0

        while self.is_running:
            time.sleep(self.check_interval)
            check_count += 1

            if not self.is_running:
                break

            is_playing = self.is_video_playing(change_threshold)

            if is_playing:
                consecutive_stopped = 0
                self.log_message(f"第{check_count}次检测: 视频播放中 ✓")
            else:
                consecutive_stopped += 1
                self.log_message(f"第{check_count}次检测: 检测到停止 ({consecutive_stopped}/{max_consecutive}) ⏸")

                if consecutive_stopped >= max_consecutive:
                    self.log_message("✅ 视频播放完成！")
                    break

    def close_current_tab(self):
        """关闭当前标签页"""
        try:
            self.log_message("🗑️ 关闭当前播放完成的标签页...")

            # 确保Chrome窗口处于前台
            hwnd = self.chrome_window['hwnd']
            if win32gui.IsWindow(hwnd):
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(1)

            # 使用Ctrl+W关闭当前标签页
            pyautogui.hotkey('ctrl', 'w')
            time.sleep(1)

            self.log_message("✓ 当前标签页已关闭")

        except Exception as e:
            self.log_message(f"❌ 关闭标签页失败: {e}")

    def refresh_all_tabs(self, remaining_tabs):
        """刷新所有剩余的标签页"""
        try:
            self.log_message(f"🔄 开始刷新所有剩余的 {remaining_tabs} 个标签页...")

            # 确保Chrome窗口处于前台
            hwnd = self.chrome_window['hwnd']
            if win32gui.IsWindow(hwnd):
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(1)

            # 从第一个标签页开始，逐个刷新
            for i in range(1, remaining_tabs + 1):
                if not self.is_running:
                    break

                try:
                    self.log_message(f"  刷新第 {i} 个标签页...")

                    # 切换到标签页
                    if i <= 8:
                        pyautogui.hotkey('ctrl', str(i))
                    else:
                        # 超过8个标签页时，先回到第1个，然后用Ctrl+Tab切换
                        pyautogui.hotkey('ctrl', '1')
                        time.sleep(0.5)
                        for _ in range(i - 1):
                            pyautogui.hotkey('ctrl', 'tab')
                            time.sleep(0.3)

                    time.sleep(1)

                    # 刷新当前标签页
                    pyautogui.press('f5')
                    time.sleep(2)  # 等待页面刷新

                except Exception as e:
                    self.log_message(f"  ❌ 刷新第 {i} 个标签页失败: {e}")

            self.log_message("✓ 所有标签页刷新完成")

        except Exception as e:
            self.log_message(f"❌ 刷新标签页失败: {e}")

    def study_loop(self, tab_count, url_pattern, stop_count, change_threshold):
        """学习循环"""
        self.log_message(f"📋 准备学习 {tab_count} 个标签页")

        video_count = 0
        skipped_count = 0
        current_tab_index = 1  # 当前要处理的标签页索引
        original_tab_count = tab_count  # 保存原始标签页数量

        while current_tab_index <= tab_count and self.is_running:
            self.log_message(f"{'='*25} 标签页 {current_tab_index}/{original_tab_count} {'='*25}")
            self.update_status(f"处理标签页 {current_tab_index}/{original_tab_count}")

            # 切换标签页
            if not self.switch_to_tab(current_tab_index):
                self.log_message(f"❌ 切换失败，跳过第 {current_tab_index} 个标签页")
                skipped_count += 1
                current_tab_index += 1
                continue

            # 检测是否是视频页面
            if not self.is_video_page(url_pattern):
                current_url = self.get_current_url()
                url_display = current_url[:60] + "..." if len(current_url) > 60 else current_url
                self.log_message(f"⏭️ 第 {current_tab_index} 个标签页不是视频页面，跳过")
                self.log_message(f"   URL: {url_display}")
                skipped_count += 1
                current_tab_index += 1
                continue

            # 这是视频页面，开始学习
            video_count += 1
            self.log_message(f"🎯 开始学习第 {video_count} 个视频")
            self.update_status(f"学习第 {video_count} 个视频")

            # 启动播放
            self.start_video()

            # 等待播放完成
            self.wait_for_completion(stop_count, change_threshold)

            if self.is_running:
                self.log_message(f"✅ 第 {video_count} 个视频学习完成")

                # 如果还有剩余标签页，执行关闭窗口和刷新操作
                if current_tab_index < tab_count:
                    self.log_message("🔄 视频播放完成，开始后续处理...")

                    # 关闭当前播放完成的标签页
                    self.close_current_tab()

                    # 关闭标签页后，总数减1，但当前索引不变（因为后面的标签页会前移）
                    tab_count -= 1

                    # 刷新所有剩余标签页
                    self.refresh_all_tabs(tab_count)

                    self.log_message("⏳ 准备切换到下一个标签页...")
                    time.sleep(2)
                    # 注意：不增加current_tab_index，因为关闭标签页后，下一个标签页会移到当前位置
                else:
                    # 这是最后一个标签页，处理完成后退出
                    break
            else:
                break

        # 显示完成统计
        self.log_message("=" * 70)
        self.log_message("🎉 学习任务完成！")
        self.log_message(f"📊 学习统计:")
        self.log_message(f"   原始标签页数: {original_tab_count}")
        self.log_message(f"   视频页面数: {video_count}")
        self.log_message(f"   跳过页面数: {skipped_count}")
        self.log_message(f"   已关闭标签页数: {video_count}")  # 播放完成的视频标签页都被关闭了
        self.log_message("=" * 70)

        self.update_status("学习完成")

def main():
    root = tk.Tk()
    app = VideoStudyGUI(root)

    # 设置窗口关闭事件
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("退出", "程序正在运行，确定要退出吗？"):
                app.stop_study()
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
