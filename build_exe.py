#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
继续教育视频学习工具 - 打包为exe程序
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(message):
    """打印步骤信息"""
    print(f"\n{'='*50}")
    print(f"🔧 {message}")
    print(f"{'='*50}")

def print_success(message):
    """打印成功信息"""
    print(f"✅ {message}")

def print_error(message):
    """打印错误信息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息"""
    print(f"ℹ️  {message}")

def check_python():
    """检查Python环境"""
    print_step("检查Python环境")
    
    try:
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True, check=True)
        python_version = result.stdout.strip()
        print_success(f"Python环境检查通过: {python_version}")
        return True
    except Exception as e:
        print_error(f"Python环境检查失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print_step("安装依赖包")
    
    dependencies = [
        "pyinstaller",
        "pyautogui", 
        "pillow",
        "pywin32",
        "pyperclip"
    ]
    
    for dep in dependencies:
        try:
            print_info(f"正在安装 {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                  capture_output=True, text=True, check=True)
            print_success(f"{dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print_error(f"{dep} 安装失败: {e}")
            print_info(f"错误输出: {e.stderr}")
            return False
        except Exception as e:
            print_error(f"安装 {dep} 时发生异常: {e}")
            return False
    
    print_success("所有依赖包安装完成")
    return True

def build_exe():
    """打包为exe文件"""
    print_step("开始打包程序")
    
    # 检查源文件是否存在
    source_file = "video_study_gui.py"
    if not os.path.exists(source_file):
        print_error(f"源文件 {source_file} 不存在")
        return False
    
    print_info(f"源文件: {source_file}")
    
    # PyInstaller命令参数
    cmd = [
        sys.executable, "-m", "pyinstaller",
        "--onefile",                    # 打包为单个exe文件
        "--windowed",                   # 不显示控制台窗口
        "--name=继续教育视频学习工具",     # 指定exe文件名
        "--icon=NONE",                  # 不使用图标
        "--clean",                      # 清理临时文件
        source_file
    ]
    
    try:
        print_info("正在执行打包命令...")
        print_info(f"命令: {' '.join(cmd)}")
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        print_success("打包命令执行成功")
        
        # 检查生成的exe文件
        exe_path = Path("dist") / "继续教育视频学习工具.exe"
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print_success(f"exe文件生成成功!")
            print_info(f"文件位置: {exe_path.absolute()}")
            print_info(f"文件大小: {file_size:.1f} MB")
            return True
        else:
            print_error("exe文件未生成")
            return False
            
    except subprocess.CalledProcessError as e:
        print_error(f"打包失败: {e}")
        print_info(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print_error(f"打包过程中发生异常: {e}")
        return False

def cleanup():
    """清理临时文件"""
    print_step("清理临时文件")
    
    cleanup_items = [
        "build",
        "__pycache__",
        "video_study_gui.spec",
        "继续教育视频学习工具.spec"
    ]
    
    for item in cleanup_items:
        try:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print_info(f"删除目录: {item}")
                else:
                    os.remove(item)
                    print_info(f"删除文件: {item}")
        except Exception as e:
            print_error(f"删除 {item} 失败: {e}")
    
    print_success("临时文件清理完成")

def main():
    """主函数"""
    print("🎓 继续教育视频学习工具 - 打包程序")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print_info(f"当前目录: {current_dir}")
    
    # 步骤1: 检查Python环境
    if not check_python():
        print_error("Python环境检查失败，无法继续")
        input("按回车键退出...")
        return False
    
    # 步骤2: 安装依赖包
    if not install_dependencies():
        print_error("依赖包安装失败，无法继续")
        input("按回车键退出...")
        return False
    
    # 步骤3: 打包程序
    if not build_exe():
        print_error("程序打包失败")
        input("按回车键退出...")
        return False
    
    # 步骤4: 清理临时文件
    cleanup()
    
    # 完成
    print_step("打包完成")
    print_success("🎉 程序打包成功!")
    print_info("exe文件位于 dist 目录中")
    print_info("您现在可以将exe文件复制到任何Windows电脑上运行")
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_error("\n用户中断了打包过程")
    except Exception as e:
        print_error(f"打包过程中发生未预期的错误: {e}")
        input("按回车键退出...")
